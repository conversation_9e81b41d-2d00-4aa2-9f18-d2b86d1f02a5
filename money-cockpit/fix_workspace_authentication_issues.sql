-- Fix Workspace Authentication and Privacy Issues
-- Run this script in your Supabase SQL Editor

-- 1. Add is_private column to workspaces table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'workspaces' 
        AND column_name = 'is_private'
    ) THEN
        ALTER TABLE workspaces ADD COLUMN is_private boolean DEFAULT false;
    END IF;
END $$;

-- 2. Drop the old create_workspace function and create a new one with is_private support
DROP FUNCTION IF EXISTS create_workspace(text, uuid);
DROP FUNCTION IF EXISTS create_workspace(text, uuid, boolean);

CREATE OR REPLACE FUNCTION create_workspace(p_name text, p_user_id uuid, p_is_private boolean DEFAULT false)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_workspace_id uuid;
  v_result json;
BEGIN
  -- Validate inputs
  IF p_name IS NULL OR trim(p_name) = '' THEN
    RAISE EXCEPTION 'Workspace name is required';
  END IF;
  
  -- Create the workspace
  INSERT INTO workspaces (name, is_private)
  VALUES (p_name, p_is_private)
  RETURNING id INTO v_workspace_id;
  
  -- Add the user as admin
  INSERT INTO workspace_users (workspace_id, user_id, role)
  VALUES (v_workspace_id, p_user_id, 'admin');
  
  -- Return the created workspace
  SELECT json_build_object(
    'id', id,
    'name', name,
    'is_private', is_private,
    'created_at', created_at
  ) INTO v_result
  FROM workspaces
  WHERE id = v_workspace_id;
  
  RETURN v_result;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Failed to create workspace: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_workspace(text, uuid, boolean) TO authenticated;

-- 3. Update get_user_workspaces function to include is_private
CREATE OR REPLACE FUNCTION get_user_workspaces()
RETURNS TABLE(
  workspace_id uuid,
  workspace_name text,
  user_role text,
  is_private boolean
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    w.id as workspace_id,
    w.name as workspace_name,
    wu.role as user_role,
    COALESCE(w.is_private, false) as is_private
  FROM workspaces w
  JOIN workspace_users wu on w.id = wu.workspace_id
  WHERE wu.user_id = auth.uid();
$$;

-- 4. Ensure workspace_invitations table exists with proper structure
CREATE TABLE IF NOT EXISTS workspace_invitations (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  workspace_id uuid REFERENCES workspaces ON DELETE CASCADE NOT NULL,
  email text NOT NULL,
  role text NOT NULL CHECK (role IN ('admin', 'viewer')),
  token uuid DEFAULT uuid_generate_v4() NOT NULL,
  invited_by uuid REFERENCES auth.users NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  expires_at timestamptz DEFAULT (now() + interval '7 days') NOT NULL,
  accepted_at timestamptz,
  UNIQUE(workspace_id, email)
);

-- Enable RLS on workspace_invitations if not already enabled
ALTER TABLE workspace_invitations ENABLE ROW LEVEL SECURITY;

-- 5. Fix workspace policies - drop conflicting ones and create clean ones
DROP POLICY IF EXISTS "workspace_admin_write" ON workspaces;
DROP POLICY IF EXISTS "workspace_admin_insert" ON workspaces;
DROP POLICY IF EXISTS "workspace_insert" ON workspaces;
DROP POLICY IF EXISTS "workspace_admin_update" ON workspaces;
DROP POLICY IF EXISTS "workspace_admin_delete" ON workspaces;

-- Allow authenticated users to create workspaces
CREATE POLICY "workspace_insert" ON workspaces
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Allow users to read workspaces they belong to
CREATE POLICY "workspace_read" ON workspaces 
FOR SELECT
USING (EXISTS (
  SELECT 1 FROM workspace_users 
  WHERE workspace_id = workspaces.id 
  AND user_id = auth.uid()
));

-- Allow admins to update/delete their workspaces
CREATE POLICY "workspace_admin_update" ON workspaces
FOR UPDATE
USING (EXISTS (
  SELECT 1 FROM workspace_users 
  WHERE workspace_id = workspaces.id 
  AND user_id = auth.uid() 
  AND role = 'admin'
));

CREATE POLICY "workspace_admin_delete" ON workspaces
FOR DELETE
USING (EXISTS (
  SELECT 1 FROM workspace_users 
  WHERE workspace_id = workspaces.id 
  AND user_id = auth.uid() 
  AND role = 'admin'
));

-- 6. Fix workspace_invitations policies
DROP POLICY IF EXISTS "workspace_invitations_admin_all" ON workspace_invitations;
DROP POLICY IF EXISTS "workspace_invitations_recipient_read" ON workspace_invitations;

-- Allow workspace admins to manage invitations
CREATE POLICY "workspace_invitations_admin_all" ON workspace_invitations
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM workspace_users
    WHERE workspace_id = workspace_invitations.workspace_id
    AND user_id = auth.uid()
    AND role = 'admin'
  )
);

-- Allow users to view invitations sent to their email
CREATE POLICY "workspace_invitations_recipient_read" ON workspace_invitations
FOR SELECT
USING (
  email = (SELECT email FROM auth.users WHERE id = auth.uid())
);

-- 7. Ensure profiles table exists and has proper RLS
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  email text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Allow users to read their own profile and profiles of users in their workspaces
DROP POLICY IF EXISTS "profiles_read" ON profiles;
CREATE POLICY "profiles_read" ON profiles
FOR SELECT
USING (
  id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM workspace_users wu1
    JOIN workspace_users wu2 ON wu1.workspace_id = wu2.workspace_id
    WHERE wu1.user_id = auth.uid() AND wu2.user_id = profiles.id
  )
);

-- Allow users to update their own profile
DROP POLICY IF EXISTS "profiles_update" ON profiles;
CREATE POLICY "profiles_update" ON profiles
FOR UPDATE
USING (id = auth.uid());

-- Allow users to insert their own profile
DROP POLICY IF EXISTS "profiles_insert" ON profiles;
CREATE POLICY "profiles_insert" ON profiles
FOR INSERT
WITH CHECK (id = auth.uid());

-- 8. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- 9. Test the setup
SELECT 'Setup completed successfully. You can now test workspace creation and team invitations.' as status;
