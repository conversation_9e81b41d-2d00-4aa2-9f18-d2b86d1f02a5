import { NextRequest, NextResponse } from 'next/server'
import { createRouteClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    // Create a route handler client
    const supabase = createRouteClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ 
        error: 'Not authenticated',
        details: userError?.message 
      }, { status: 401 })
    }

    // Test if we can call the create_workspace function
    const { data: testResult, error: testError } = await supabase
      .rpc('create_workspace', {
        p_name: 'Test Workspace ' + Date.now(),
        p_user_id: user.id
      })

    if (testError) {
      return NextResponse.json({
        error: 'Database function error',
        details: testError.message,
        code: testError.code,
        hint: testError.hint
      }, { status: 400 })
    }

    // Clean up the test workspace
    if (testResult && testResult.id) {
      await supabase
        .from('workspaces')
        .delete()
        .eq('id', testResult.id)
    }

    return NextResponse.json({ 
      success: true,
      user: {
        id: user.id,
        email: user.email
      },
      testResult
    })
    
  } catch (error) {
    console.error('Error in test workspace creation:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}
