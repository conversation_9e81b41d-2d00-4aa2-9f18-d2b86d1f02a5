import { NextRequest, NextResponse } from 'next/server'
import { createRouteClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    // Create a route handler client
    const supabase = await createRouteClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ 
        error: 'Not authenticated',
        details: userError?.message 
      }, { status: 401 })
    }

    // Get workspace_id from query params
    const { searchParams } = new URL(request.url)
    const workspaceId = searchParams.get('workspace_id')
    
    if (!workspaceId) {
      return NextResponse.json({ 
        error: 'Workspace ID is required' 
      }, { status: 400 })
    }

    // Use service role to bypass RLS issues
    const { createClient } = await import('@supabase/supabase-js')
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get pending invitations
    const { data: invitationsData, error: invitationsError } = await supabaseAdmin
      .from('workspace_invitations')
      .select('id, email, role, created_at, expires_at, invited_by')
      .eq('workspace_id', workspaceId)
      .is('accepted_at', null)
      .gt('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false })

    if (invitationsError) {
      console.error('Error fetching invitations:', invitationsError)
      return NextResponse.json({ 
        error: 'Failed to fetch invitations',
        details: invitationsError.message 
      }, { status: 500 })
    }

    if (!invitationsData || invitationsData.length === 0) {
      return NextResponse.json({ invitations: [] })
    }

    // Get profiles for inviters
    const inviterIds = [...new Set(invitationsData.map(i => i.invited_by))]
    const { data: profilesData, error: profilesError } = await supabaseAdmin
      .from('profiles')
      .select('id, email')
      .in('id', inviterIds)

    if (profilesError) {
      console.error('Error fetching inviter profiles:', profilesError)
      // Continue without inviter emails
      return NextResponse.json({ 
        invitations: invitationsData.map(invitation => ({
          ...invitation,
          profiles: undefined
        }))
      })
    }

    // Combine the data
    const invitationsWithProfiles = invitationsData.map(invitation => ({
      ...invitation,
      profiles: profilesData?.find(p => p.id === invitation.invited_by) ? {
        email: profilesData.find(p => p.id === invitation.invited_by)!.email
      } : undefined
    }))

    return NextResponse.json({ invitations: invitationsWithProfiles })
    
  } catch (error) {
    console.error('Error in invitations API:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}
