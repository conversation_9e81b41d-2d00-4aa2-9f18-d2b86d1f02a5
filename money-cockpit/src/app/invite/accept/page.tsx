'use client'

import { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { createBrowserClient } from '@/lib/supabase-client'
import { CheckCircle, XCircle, Clock, Mail } from 'lucide-react'

export default function AcceptInvitePage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'expired' | 'already-accepted'>('loading')
  const [invitation, setInvitation] = useState<any>(null)
  const [error, setError] = useState<string>('')
  const searchParams = useSearchParams()
  const router = useRouter()
  const supabase = createBrowserClient()

  useEffect(() => {
    const token = searchParams.get('token')
    if (!token) {
      setStatus('error')
      setError('Invalid invitation link')
      return
    }

    acceptInvitation(token)
  }, [searchParams])

  const acceptInvitation = async (token: string) => {
    try {
      // First, get the invitation details
      const { data: invitationData, error: inviteError } = await supabase
        .from('workspace_invitations')
        .select(`
          id,
          workspace_id,
          email,
          role,
          expires_at,
          accepted_at,
          workspaces (
            name
          )
        `)
        .eq('token', token)
        .single()

      if (inviteError || !invitationData) {
        setStatus('error')
        setError('Invitation not found')
        return
      }

      setInvitation(invitationData)

      // Check if already accepted
      if (invitationData.accepted_at) {
        setStatus('already-accepted')
        return
      }

      // Check if expired
      if (new Date(invitationData.expires_at) < new Date()) {
        setStatus('expired')
        return
      }

      // Get current user
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        // User needs to sign in/up first
        // Store the token in localStorage to continue after auth
        localStorage.setItem('pendingInvitationToken', token)
        router.push('/auth/signin?message=Please sign in to accept the workspace invitation')
        return
      }

      // Check if user's email matches invitation email
      if (user.email !== invitationData.email) {
        setStatus('error')
        setError(`This invitation is for ${invitationData.email}, but you're signed in as ${user.email}`)
        return
      }

      // Accept the invitation
      const response = await fetch('/api/invite/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token })
      })

      const result = await response.json()

      if (!response.ok) {
        setStatus('error')
        setError(result.error || 'Failed to accept invitation')
        return
      }

      setStatus('success')
      
      // Redirect to workspace after a delay
      setTimeout(() => {
        router.push('/dashboard')
      }, 3000)

    } catch (error) {
      console.error('Error accepting invitation:', error)
      setStatus('error')
      setError('An unexpected error occurred')
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-16 w-16 text-green-500" />
      case 'error':
        return <XCircle className="h-16 w-16 text-red-500" />
      case 'expired':
        return <Clock className="h-16 w-16 text-orange-500" />
      case 'already-accepted':
        return <Mail className="h-16 w-16 text-blue-500" />
      default:
        return <div className="h-16 w-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
    }
  }

  const getStatusMessage = () => {
    switch (status) {
      case 'loading':
        return {
          title: 'Processing Invitation...',
          message: 'Please wait while we process your workspace invitation.'
        }
      case 'success':
        return {
          title: 'Welcome to the Team!',
          message: `You've successfully joined ${invitation?.workspaces?.name}. Redirecting to your dashboard...`
        }
      case 'error':
        return {
          title: 'Invitation Error',
          message: error
        }
      case 'expired':
        return {
          title: 'Invitation Expired',
          message: 'This invitation has expired. Please ask for a new invitation.'
        }
      case 'already-accepted':
        return {
          title: 'Already Accepted',
          message: `You've already accepted this invitation to ${invitation?.workspaces?.name}.`
        }
      default:
        return {
          title: 'Processing...',
          message: 'Please wait...'
        }
    }
  }

  const statusInfo = getStatusMessage()

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
        <div className="flex justify-center mb-6">
          {getStatusIcon()}
        </div>
        
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          {statusInfo.title}
        </h1>
        
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          {statusInfo.message}
        </p>

        {invitation && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6 text-left">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Invitation Details</h3>
            <div className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
              <p><strong>Workspace:</strong> {invitation.workspaces?.name}</p>
              <p><strong>Role:</strong> {invitation.role.charAt(0).toUpperCase() + invitation.role.slice(1)}</p>
              <p><strong>Email:</strong> {invitation.email}</p>
            </div>
          </div>
        )}

        {status === 'error' && (
          <button
            onClick={() => router.push('/dashboard')}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Go to Dashboard
          </button>
        )}

        {status === 'expired' && (
          <button
            onClick={() => router.push('/dashboard')}
            className="w-full bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Go to Dashboard
          </button>
        )}

        {status === 'already-accepted' && (
          <button
            onClick={() => router.push('/dashboard')}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Go to Dashboard
          </button>
        )}
      </div>
    </div>
  )
}
