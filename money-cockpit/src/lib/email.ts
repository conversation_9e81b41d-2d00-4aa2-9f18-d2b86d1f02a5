import { Resend } from 'resend'

const resend = new Resend(process.env.RESEND_API_KEY)

interface SendInvitationEmailParams {
  to: string
  workspaceName: string
  inviterEmail: string
  role: string
  invitationToken: string
  expiresAt: string
}

export async function sendInvitationEmail({
  to,
  workspaceName,
  inviterEmail,
  role,
  invitationToken,
  expiresAt
}: SendInvitationEmailParams) {
  const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'
  const acceptUrl = `${appUrl}/invite/accept?token=${invitationToken}`
  const expiryDate = new Date(expiresAt).toLocaleDateString()

  try {
    const { data, error } = await resend.emails.send({
      from: 'Money Cockpit <<EMAIL>>', // Using Resend's test domain for development
      to: [to],
      subject: `You're invited to join ${workspaceName} on Money Cockpit`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Workspace Invitation</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Money Cockpit</h1>
            <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Workspace Invitation</p>
          </div>
          
          <div style="background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0;">
            <h2 style="color: #333; margin-top: 0;">You're invited to join a workspace!</h2>
            
            <p style="font-size: 16px; margin-bottom: 20px;">
              <strong>${inviterEmail}</strong> has invited you to join the <strong>${workspaceName}</strong> workspace on Money Cockpit.
            </p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; margin: 20px 0;">
              <p style="margin: 0; font-size: 14px; color: #666;">
                <strong>Workspace:</strong> ${workspaceName}<br>
                <strong>Role:</strong> ${role.charAt(0).toUpperCase() + role.slice(1)}<br>
                <strong>Invited by:</strong> ${inviterEmail}<br>
                <strong>Expires:</strong> ${expiryDate}
              </p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${acceptUrl}" 
                 style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; font-size: 16px;">
                Accept Invitation
              </a>
            </div>
            
            <p style="font-size: 14px; color: #666; margin-top: 30px;">
              If the button doesn't work, copy and paste this link into your browser:<br>
              <a href="${acceptUrl}" style="color: #667eea; word-break: break-all;">${acceptUrl}</a>
            </p>
            
            <p style="font-size: 12px; color: #999; margin-top: 30px; border-top: 1px solid #e0e0e0; padding-top: 20px;">
              This invitation will expire on ${expiryDate}. If you don't want to receive these emails, you can ignore this message.
            </p>
          </div>
        </body>
        </html>
      `,
      text: `
You're invited to join ${workspaceName} on Money Cockpit!

${inviterEmail} has invited you to join the ${workspaceName} workspace as a ${role}.

To accept this invitation, visit: ${acceptUrl}

Workspace: ${workspaceName}
Role: ${role.charAt(0).toUpperCase() + role.slice(1)}
Invited by: ${inviterEmail}
Expires: ${expiryDate}

This invitation will expire on ${expiryDate}.
      `
    })

    if (error) {
      console.error('Error sending invitation email:', error)
      throw new Error(`Failed to send email: ${error.message}`)
    }

    console.log('Invitation email sent successfully:', data)
    return { success: true, data }
  } catch (error) {
    console.error('Error in sendInvitationEmail:', error)
    throw error
  }
}

// Test function to verify email configuration
export async function testEmailConfiguration() {
  if (!process.env.RESEND_API_KEY || process.env.RESEND_API_KEY === 'your_resend_api_key_here') {
    throw new Error('RESEND_API_KEY is not configured. Please set a valid Resend API key in your .env file.')
  }

  try {
    // Just test that we can create a Resend instance and the API key format is valid
    // We'll use Resend's testing email address for development
    const testEmail = '<EMAIL>' // Resend's official test email

    const { data, error } = await resend.emails.send({
      from: 'Money Cockpit <<EMAIL>>', // Use Resend's test domain
      to: [testEmail],
      subject: 'Money Cockpit - Email Configuration Test',
      text: 'This is a test email to verify Resend configuration for Money Cockpit workspace invitations.'
    })

    if (error) {
      throw new Error(`Email configuration test failed: ${error.message}`)
    }

    return {
      success: true,
      message: 'Email configuration is working correctly',
      testEmailSent: testEmail,
      emailId: data?.id
    }
  } catch (error) {
    throw error
  }
}
